@font-face {
  font-family: '思源黑体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源黑体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 10vw;
  height: 10vw;
  top: 3.6vw;
  right: 2.8vw;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源黑体';
  background: linear-gradient(180deg, #fdf9ec 0%, #e3e3e3 100%);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #0464e0;
  background: #aae7f7 url(../img/bj.jpg) no-repeat center bottom / 100vw auto;
}
.warp .page .logo {
  width: 45.7333vw;
  height: 10.9333vw;
  background: #fff url(../img/logo.png) no-repeat center center / 90% auto;
  border-radius: 0 0 2vw 2vw;
  position: absolute;
  top: 0;
}
.warp .page .title {
  margin-top: -50vw;
  width: 86.533vw;
  z-index: 2;
}
.warp .page .time {
  margin-top: 3vw;
  height: 9.3333vw;
  background: url(../img/time.png) no-repeat center top / 29.6vw auto;
  padding-top: 6vw;
  font-size: 3vw;
  font-weight: 500;
}
.warp .page .address {
  margin-top: 3vw;
  width: 39.6vw;
  height: 9.3333vw;
  background: url(../img/address.png) no-repeat center center / 100% 100%;
}
.warp .page .bg2 {
  width: 100vw;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 5vw;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  padding: 0 1vw;
}
.warp .page .button_container .btn {
  width: 25.0667vw;
  margin: 0 2vw 2vw;
}
.warp .page .hand {
  width: 30vw;
  margin-top: 4vw;
}
.warp .page .header {
  width: 100vw;
  height: 146.4vw;
  background: url(../img/header.jpg) no-repeat center center / 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 4.4vw;
  font-weight: bold;
  padding-top: 40vw;
  letter-spacing: 0.2vw;
  flex-shrink: 0;
}
.warp .page .content {
  margin-top: -30vw;
  flex-shrink: 0;
}
.warp .page .site {
  width: 100vw;
}
.warp .page .back {
  margin: 6vw 0 20vw;
  width: 32vw;
  height: 9.2vw;
  border: none;
  border-radius: 4.6vw;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #e53935 100%);
  color: #fff;
  font-size: 4vw;
  font-weight: bold;
  text-shadow: 0 0.5vw 1vw rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1vw 2vw rgba(0, 0, 0, 0.2));
  box-shadow: inset 0 0.5vw 1vw rgba(255, 255, 255, 0.3), inset 0 -0.5vw 1vw rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .back::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}
.warp .page .back:hover {
  transform: translateY(-0.5vw) scale(1.02);
  filter: drop-shadow(0 1.5vw 3vw rgba(0, 0, 0, 0.25));
  background: linear-gradient(135deg, #ff7979 0%, #ff6348 50%, #e74c3c 100%);
}
.warp .page .back:hover::before {
  left: 100%;
}
.warp .page .back:active {
  transform: translateY(0.2vw) scale(0.98);
  filter: drop-shadow(0 0.5vw 1vw rgba(0, 0, 0, 0.15));
  background: linear-gradient(135deg, #e55656 0%, #e74c3c 50%, #c0392b 100%);
}
.warp .bj2 {
  background: #40b6ff;
  height: 100vh;
  overflow-y: auto;
}
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.9);
  }
}
@keyframes scoreChangeAnimation {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5vw) scale(1.2);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10vw) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20vw) scale(0.8);
  }
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.area {
  margin-top: -8vw;
  width: 93.6vw;
  height: 126.8vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .stit {
  margin-top: 10vw;
  width: 46vw;
}
.area .spirit1 {
  position: absolute;
  width: 30.4vw;
  left: -9vw;
  bottom: -6vw;
}
.area .back {
  position: absolute;
  bottom: -4vw;
  width: 45.3333vw;
}
.area .submit {
  position: absolute;
  bottom: -15vw;
  width: 40vw;
}
.area .rule {
  width: 100%;
  padding: 0 4vw;
  margin: 2vw 0 10vw;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0.1vw;
  position: relative;
}
.area .prize {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 2vw;
}
.area .prize .info {
  padding: 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 16vw;
  width: 70vw;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 5vw;
  line-height: 7vw;
  max-width: 75vw;
  text-align: center;
}
.area .prize .info .jptit {
  width: 29.467vw;
  margin-bottom: 2vw;
}
.area .prize .edit {
  width: 45.3333vw;
}
.area .form {
  width: 100%;
  padding: 16vw 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 5vw;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 22vw;
  font-weight: bold;
  font-size: 4.6vw;
  white-space: nowrap;
  color: #352219;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 3vw;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0.8vw;
  padding-left: 2.5333vw;
  width: 50vw;
  height: 7.7333vw;
  border: 1px #352219 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #352219;
  font-size: 4.6vw;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -10vw;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #352219;
}
.area .form .form-footer .fz1 {
  font-size: 6vw;
}
.area .form .form-footer p {
  font-size: 6vw;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0.3vw;
}
.area .form .button {
  margin-top: -5vw;
  width: 30.4vw;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0.5vw;
}
.area2 {
  margin-top: 0;
  width: 87.2vw;
  height: 136.5333vw;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area2 .stit {
  margin-top: -21vw;
  margin-bottom: 10vw;
}
.area2 .back {
  bottom: 8vw;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 42.6667vw;
  position: absolute;
  bottom: -6vw;
}
.mask .popup1 {
  width: 87.8667vw;
  height: 68.2667vw;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 75.4667vw;
  height: 64.8vw;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 10.8vw;
  position: absolute;
  top: 0;
  right: -2vw;
}
.mask .popup3 {
  width: 75.4667vw;
  height: 60.8vw;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .back {
  bottom: 9vw;
}
.mask .popup4 {
  width: 75.4667vw;
  height: 60.8vw;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup4 .back {
  bottom: 9vw;
}
.mask .popup5 {
  width: 75.4667vw;
  height: 66.1333vw;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 0vw;
}
.mask .popup5 .p3 {
  margin-top: -4vw;
  font-size: 7vw;
  white-space: nowrap;
}
.mask .popup5 .p4 {
  font-size: 7vw;
  white-space: nowrap;
}
.mask .popup5 .back {
  bottom: 9vw;
}
.mask .popup6 {
  width: 75.4667vw;
  height: 66.1333vw;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
.mask .popup6 .back {
  bottom: 9vw;
}
