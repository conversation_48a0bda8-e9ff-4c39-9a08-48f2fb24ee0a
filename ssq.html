<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>徽行送清凉 致敬劳动者</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="page bj2">
            <div class="logo animate__animated animate__fadeInDown"></div>
            <div class="header">徽商银行合肥分行蜀山区网点</div>
            <div class="content">
                <img :src="'img/ssq'+(index+1)+'.png'" class="site" v-for="(item,index) in list" @click="gomap(item)">
            </div>
            <div class="back" @click="back">返回首页</div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script src="js/bank-data.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
        const app = createApp({
            setup() {
                setMockPage && setMockPage()//添加案例提示语
                const list = ref([])
                const loading = ref(true)

                // 加载蜀山区网点数据
                const loadBranchData = async () => {
                    try {
                        await window.bankDataManager.loadBankData()
                        const branches = window.bankDataManager.getBranchesByDistrict('蜀山区')
                        list.value = branches
                        loading.value = false
                    } catch (error) {
                        console.error('加载网点数据失败:', error)
                        loading.value = false
                    }
                }

                // 页面加载时获取数据
                loadBranchData()
                const gomap = (item)=>{
                    if (!item.gps || item.gps.trim() === '') {
                        // 如果没有GPS坐标，显示提示信息
                        if (typeof vant !== 'undefined' && vant.showToast) {
                            vant.showToast('该网点暂无GPS坐标信息')
                        } else {
                            alert('该网点暂无GPS坐标信息')
                        }
                        return
                    }

                    wx.openLocation({
                        latitude: +item.gps.split(',')[0], // 纬度，浮点数，范围为90 ~ -90
                        longitude: +item.gps.split(',')[1], // 经度，浮点数，范围为180 ~ -180。
                        name: item.name, // 位置名
                        address: item.address, // 地址详情说明
                        scale: 16, // 地图缩放级别,整形值,范围从1~28。默认为最大
                    });
                }
                const back = ()=>{
                    // window.history.go(-1)
                    location.replace('index.html')
                }
                return {
                    list,
                    loading,
                    gomap,
                    back
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>

</html>



