<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>徽行送清凉 致敬劳动者</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="page bj2">
            <div class="logo animate__animated animate__fadeInDown"></div>
            <div class="header">徽商银行合肥分行蜀山区网点</div>
            <div class="content">
                <img :src="'img/ssq'+(index+1)+'.png'" class="site" v-for="(item,index) in list" @click="gomap(item)">
            </div>
            <div class="back" @click="back">返回首页</div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
        const app = createApp({
            setup() {
                setMockPage && setMockPage()//添加案例提示语
                const list = [
                    {name:'安徽自贸试验区合肥片区支行',address:'合肥市创新大道2760号高新区公租房1、2层部分门面房',gps:'31.83367,117.134962'},
                    {name:'高新开发区支行',address:'合肥市长江西路848号',gps:'31.856236,117.207488'},
                    {name:'合作化路支行',address:'合肥市合作化南路与太湖路交口南侧合安大厦一、二层',gps:'31.833019,117.251972'},
                    {name:'五里墩支行（原址装修中，正在与合作化路支行合署办公）',address:'合肥市合作化南路与太湖路交口南侧合安大厦一、二层',gps:'31.833019,117.251972'},
                    {name:'怀宁路支行',address:'合肥市望江西路与怀宁路交口西南角信达西山银杏第11#楼商业门面',gps:'31.832854,117.221392'},
                    {name:'经济开发区支行',address:'合肥市芙蓉西路9号',gps:'31.820004389244872,117.22738333990715'},
                    {name:'清潭路支行',address:'合肥市经开区锦绣大道以南清潭路以西中德教育合作示范基地公共平台项目8号楼',gps:'31.749132,117.264717'},
                    {name:'南七支行',address:'合肥市蜀山区望江西路1号南七文创中心A06地块商业L1-13-1、L2-15-16商铺',gps:'31.828718,117.26313'},
                    {name:'祁门路支行',address:'合肥市祁门路1718号天鹅湖购物中心B座商业门面',gps:'31.80968,117.212134'},
                    {name:'潜山路支行',address:'合肥市潜山路478号国瑞大厦1、2层',gps:'31.862037,117.231258'},
                    {name:'三里庵支行',address:'合肥市梅山路103号',gps:'31.851705,117.262171'},
                    {name:'蜀山支行',address:'合肥市黄山路455号新华学府花园东区综合楼1、2楼',gps:'31.840716,117.195365'},
                    {name:'天鹅湖支行',address:'合肥市东流路999号新城国际大厦A座',gps:'31.814852,117.232847'},
                    {name:'习友路支行',address:'安徽省合肥市政务区习友路777号宋都西湖花苑3#商业楼101-105室',gps:'31.797961,117.220304'},
                    {name:'香樟大道支行',address:'合肥市黄山路与香樟大道交口徽商银行合肥分行办公大楼B区一层',gps:'31.840716,117.195365'},
                ]
                const gomap = (item)=>{
                    wx.openLocation({
                        latitude: +item.gps.split(',')[0], // 纬度，浮点数，范围为90 ~ -90
                        longitude: +item.gps.split(',')[1], // 经度，浮点数，范围为180 ~ -180。
                        name: item.name, // 位置名
                        address: item.address, // 地址详情说明
                        scale: 16, // 地图缩放级别,整形值,范围从1~28。默认为最大
                    });
                }
                const back = ()=>{
                    // window.history.go(-1)
                    location.replace('index.html')
                }
                return {
                    list,
                    gomap,
                    back
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>

</html>



