# 徽商银行网点数据管理系统

## 项目概述

这是一个基于Vue.js的徽商银行合肥分行网点展示系统，支持按行政区过滤显示银行网点信息。

## 文件结构

```
├── bank-branches.json          # 银行网点数据文件
├── js/
│   └── bank-data.js           # 银行数据管理类
├── index.html                 # 主页面
├── lyq.html                   # 庐阳区网点页面
├── ssq.html                   # 蜀山区网点页面
├── bhq.html                   # 包河区网点页面
├── yhq.html                   # 瑶海区网点页面
├── fdx.html                   # 肥东县网点页面
├── fxx.html                   # 肥西县网点页面
├── cfx.html                   # 长丰县网点页面
├── ljx.html                   # 庐江县网点页面
├── chs.html                   # 巢湖市网点页面
└── README.md                  # 说明文档
```

## 数据结构

### bank-branches.json

银行网点数据按行政区分组存储：

```json
{
  "庐阳区": [
    {
      "name": "支行名称",
      "address": "详细地址",
      "gps": "纬度,经度"
    }
  ],
  "包河区": [...],
  "蜀山区": [...],
  "瑶海区": [...],
  "肥东县": [...],
  "肥西县": [...],
  "长丰县": [...],
  "巢湖市": [...],
  "庐江县": [...]
}
```

## 功能特性

### 1. 数据管理类 (BankDataManager)

位于 `js/bank-data.js`，提供以下功能：

- `loadBankData()`: 异步加载银行数据
- `getBranchesByDistrict(district)`: 根据行政区获取网点数据
- `getDistricts()`: 获取所有行政区列表
- `searchBranches(keyword, district)`: 搜索网点
- `getBranchesWithGPS(district)`: 获取有GPS坐标的网点

### 2. 页面功能

每个HTML页面都包含：

- 自动加载对应行政区的网点数据
- 点击网点图标打开微信地图导航
- GPS坐标验证和错误提示
- 返回主页功能

### 3. 错误处理

- 数据加载失败处理
- GPS坐标缺失提示
- 网络请求异常处理

## 使用方法

### 1. 添加新网点

在 `bank-branches.json` 中对应行政区数组中添加新的网点对象：

```json
{
  "name": "新支行名称",
  "address": "详细地址",
  "gps": "纬度,经度"
}
```

### 2. 修改网点信息

直接编辑 `bank-branches.json` 中对应的网点数据。

### 3. 添加新行政区

1. 在 `bank-branches.json` 中添加新的行政区键值对
2. 创建对应的HTML页面
3. 在主页面 `index.html` 中添加对应的按钮

### 4. 自定义搜索

使用 `BankDataManager` 类的搜索方法：

```javascript
// 搜索包含关键词的网点
const results = await bankDataManager.searchBranches('关键词');

// 在特定区域搜索
const results = await bankDataManager.searchBranches('关键词', '庐阳区');
```

## 技术栈

- **前端框架**: Vue.js 3.2.31
- **UI组件**: Vant 4.4.1
- **工具库**: jQuery 3.6.0
- **数据格式**: JSON
- **地图服务**: 微信地图API

## 注意事项

1. **GPS坐标格式**: 必须是 "纬度,经度" 的字符串格式
2. **图片资源**: 每个页面需要对应的网点图片资源
3. **微信环境**: 地图功能需要在微信环境中使用
4. **数据同步**: 修改JSON文件后需要刷新页面才能看到更新

## 开发建议

1. 定期备份 `bank-branches.json` 数据文件
2. 添加新网点时建议先验证GPS坐标的准确性
3. 可以考虑添加数据验证功能
4. 建议添加网点搜索功能到主页面

## 维护说明

- 数据更新：直接修改 `bank-branches.json` 文件
- 页面样式：修改对应的CSS文件
- 功能扩展：可以在 `BankDataManager` 类中添加新方法
- 错误监控：建议添加日志记录功能
