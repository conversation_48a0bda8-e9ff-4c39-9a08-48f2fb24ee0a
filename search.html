<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>网点搜索 - 徽行送清凉</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .search-container {
            padding: 20px;
            background: #fff;
        }
        .search-result {
            margin-top: 20px;
        }
        .district-group {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }
        .district-title {
            background: #f5f5f5;
            padding: 10px 15px;
            font-weight: bold;
            color: #333;
        }
        .branch-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        .branch-item:last-child {
            border-bottom: none;
        }
        .branch-item:hover {
            background: #f9f9f9;
        }
        .branch-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .branch-address {
            color: #666;
            font-size: 14px;
        }
        .gps-status {
            float: right;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .has-gps {
            background: #e8f5e8;
            color: #52c41a;
        }
        .no-gps {
            background: #fff2e8;
            color: #fa8c16;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="page">
            <div class="logo animate__animated animate__fadeInDown"></div>
            <div class="search-container">
                <van-search
                    v-model="searchKeyword"
                    placeholder="搜索网点名称或地址"
                    @search="onSearch"
                    @clear="onClear"
                    show-action
                >
                    <template #action>
                        <div @click="onSearch">搜索</div>
                    </template>
                </van-search>

                <van-field
                    v-model="selectedDistrict"
                    is-link
                    readonly
                    label="筛选区域"
                    placeholder="选择行政区（可选）"
                    @click="showDistrictPicker = true"
                />

                <van-popup v-model:show="showDistrictPicker" position="bottom">
                    <van-picker
                        :columns="districtOptions"
                        @confirm="onDistrictConfirm"
                        @cancel="showDistrictPicker = false"
                    />
                </van-popup>

                <div class="search-result" v-if="searchResults.length > 0">
                    <div v-for="group in searchResults" :key="group.district" class="district-group">
                        <div class="district-title">{{ group.district }}</div>
                        <div 
                            v-for="branch in group.branches" 
                            :key="branch.name"
                            class="branch-item"
                            @click="gomap(branch)"
                        >
                            <div class="branch-name">
                                {{ branch.name }}
                                <span :class="['gps-status', branch.gps ? 'has-gps' : 'no-gps']">
                                    {{ branch.gps ? '可导航' : '无坐标' }}
                                </span>
                            </div>
                            <div class="branch-address">{{ branch.address }}</div>
                        </div>
                    </div>
                </div>

                <van-empty v-else-if="hasSearched" description="未找到相关网点" />
            </div>
            
            <div class="back" @click="back">返回首页</div>
        </div>
    </div>

    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script src="js/bank-data.js"></script>
    <script>
        const { createApp, ref } = Vue
        const app = createApp({
            setup() {
                setMockPage && setMockPage()//添加案例提示语
                
                const searchKeyword = ref('')
                const selectedDistrict = ref('')
                const showDistrictPicker = ref(false)
                const searchResults = ref([])
                const hasSearched = ref(false)
                const districtOptions = ref([])

                // 初始化数据
                const initData = async () => {
                    try {
                        await window.bankDataManager.loadBankData()
                        const districts = window.bankDataManager.getDistricts()
                        districtOptions.value = ['全部'].concat(districts)
                    } catch (error) {
                        console.error('初始化数据失败:', error)
                    }
                }

                // 搜索功能
                const onSearch = async () => {
                    if (!searchKeyword.value.trim()) {
                        vant.showToast('请输入搜索关键词')
                        return
                    }

                    try {
                        const district = selectedDistrict.value === '全部' ? null : selectedDistrict.value
                        const results = window.bankDataManager.searchBranches(searchKeyword.value, district)
                        searchResults.value = results
                        hasSearched.value = true
                        
                        if (results.length === 0) {
                            vant.showToast('未找到相关网点')
                        }
                    } catch (error) {
                        console.error('搜索失败:', error)
                        vant.showToast('搜索失败，请重试')
                    }
                }

                // 清空搜索
                const onClear = () => {
                    searchResults.value = []
                    hasSearched.value = false
                }

                // 区域选择确认
                const onDistrictConfirm = ({ selectedValues }) => {
                    selectedDistrict.value = selectedValues[0]
                    showDistrictPicker.value = false
                }

                // 打开地图导航
                const gomap = (item) => {
                    if (!item.gps || item.gps.trim() === '') {
                        vant.showToast('该网点暂无GPS坐标信息')
                        return
                    }
                    
                    wx.openLocation({
                        latitude: +item.gps.split(',')[0],
                        longitude: +item.gps.split(',')[1],
                        name: item.name,
                        address: item.address,
                        scale: 16,
                    });
                }

                // 返回首页
                const back = () => {
                    location.replace('index.html')
                }

                // 页面加载时初始化
                initData()

                return {
                    searchKeyword,
                    selectedDistrict,
                    showDistrictPicker,
                    searchResults,
                    hasSearched,
                    districtOptions,
                    onSearch,
                    onClear,
                    onDistrictConfirm,
                    gomap,
                    back
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>

</html>
