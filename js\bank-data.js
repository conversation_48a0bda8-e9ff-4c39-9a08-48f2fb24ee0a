// 银行网点数据管理
class BankDataManager {
    constructor() {
        this.bankData = null;
        this.loaded = false;
    }

    // 加载银行数据
    async loadBankData() {
        if (this.loaded) {
            return this.bankData;
        }
        
        try {
            const response = await fetch('./bank-branches.json');
            this.bankData = await response.json();
            this.loaded = true;
            return this.bankData;
        } catch (error) {
            console.error('加载银行数据失败:', error);
            return null;
        }
    }

    // 根据行政区获取网点数据
    getBranchesByDistrict(district) {
        if (!this.bankData) {
            console.error('银行数据未加载');
            return [];
        }
        
        return this.bankData[district] || [];
    }

    // 获取所有行政区列表
    getDistricts() {
        if (!this.bankData) {
            console.error('银行数据未加载');
            return [];
        }
        
        return Object.keys(this.bankData);
    }

    // 搜索网点（按名称或地址）
    searchBranches(keyword, district = null) {
        if (!this.bankData) {
            console.error('银行数据未加载');
            return [];
        }

        let results = [];
        const searchData = district ? { [district]: this.bankData[district] } : this.bankData;

        for (const [districtName, branches] of Object.entries(searchData)) {
            const filteredBranches = branches.filter(branch => 
                branch.name.includes(keyword) || 
                branch.address.includes(keyword)
            );
            
            if (filteredBranches.length > 0) {
                results.push({
                    district: districtName,
                    branches: filteredBranches
                });
            }
        }

        return results;
    }

    // 获取有GPS坐标的网点
    getBranchesWithGPS(district = null) {
        if (!this.bankData) {
            console.error('银行数据未加载');
            return [];
        }

        let results = [];
        const searchData = district ? { [district]: this.bankData[district] } : this.bankData;

        for (const [districtName, branches] of Object.entries(searchData)) {
            const branchesWithGPS = branches.filter(branch => branch.gps && branch.gps.trim() !== '');
            
            if (branchesWithGPS.length > 0) {
                results.push({
                    district: districtName,
                    branches: branchesWithGPS
                });
            }
        }

        return results;
    }
}

// 创建全局实例
window.bankDataManager = new BankDataManager();

// 导出（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BankDataManager;
}
