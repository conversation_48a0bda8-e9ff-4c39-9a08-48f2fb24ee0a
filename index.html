<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>徽行送清凉 致敬劳动者</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="page fc">
            <div class="logo animate__animated animate__fadeInDown"></div>
            <img src="img/title.png" class="title animate__animated animate__zoomIn">
            <div class="time animate__animated animate__bounceIn">2025年7月14日-2025年8月10日</div>
            <div class="address animate__animated animate__bounceIn"></div>
            <div class="button_container animate__animated animate__bounceIn">
                <img src="img/btn1.png" class="btn" @click="goLink('ssq.html')">
                <img src="img/btn2.png" class="btn" @click="goLink('lyq.html')">
                <img src="img/btn3.png" class="btn" @click="goLink('yhq.html')">
                <img src="img/btn4.png" class="btn" @click="goLink('bhq.html')">
                <img src="img/btn5.png" class="btn" @click="goLink('fdx.html')">
                <img src="img/btn6.png" class="btn" @click="goLink('fxx.html')">
                <img src="img/btn7.png" class="btn" @click="goLink('cfx.html')">
                <img src="img/btn8.png" class="btn" @click="goLink('ljx.html')">
                <img src="img/btn9.png" class="btn" @click="goLink('chs.html')">
            </div>
            <img src="img/hand.png" class="hand pulsate-bck2">
        </div>
    </div>
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
        const app = createApp({
            setup() {
                setMockPage && setMockPage()//添加案例提示语
                const goLink = e => e&&(location.href=e)
                return {
                    goLink
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>

</html>



