<?php

namespace app\zt2025\controller;

use app\BaseController;
use app\common\controller\OldController;
use app\common\model\Wechat;
use app\common\controller\ZtController;

use think\facade\View;
use think\facade\Db;

use wechat\Jsauth;
use wechat\Jssdk;

class Hhsql extends OldController
{

    public function initialize()
    {
        parent::initialize();
        $this->config_id = 204;
        $this->fid = 448;
        $this->session_id = '2025_hhsql_' . $this->fid;
        $this->tableName = 'moo_form_data_2025_4';
        $this->getConfig(); //获取微信配置信息 
        //测试授权
        $this->ztdebug('hhsql');
        //授权，key和测试授权保持一致才能起作用
        $this->Oauth('', 'snsapi_userinfo', 'hhsql');
        //统计
        totalviews_new($this->fid);

        //活动状态
        if (time() < $this->configWx['reg_start_time']) {
            $this->endtime = 1;
        } elseif (time() > $this->configWx['reg_end_time']) {
            $this->endtime = 2;
        }
    }


    public function index()
    {
        return view();
    }
    public function ssq()
    {
        return view();
    }
    public function lyq()
    {
        return view();
    }
    public function yhq()
    {
        return view();
    }
    public function bhq()
    {
        return view();
    }
    public function fdx()
    {
        return view();
    }
    public function fxx()
    {
        return view();
    }
    public function cfx()
    {
        return view();
    }
    public function ljx()
    {
        return view();
    }
    public function chs()
    {
        return view();
    }
}
