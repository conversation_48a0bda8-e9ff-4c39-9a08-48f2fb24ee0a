<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>数据管理 - 徽行送清凉</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .admin-container {
            padding: 20px;
            background: #fff;
            min-height: 100vh;
        }
        .stats-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stats-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .stats-item:last-child {
            border-bottom: none;
        }
        .stats-label {
            color: #666;
        }
        .stats-value {
            font-weight: bold;
            color: #333;
        }
        .district-detail {
            margin-top: 20px;
        }
        .district-card {
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .district-header {
            background: #f5f5f5;
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        .district-name {
            font-weight: bold;
            color: #333;
        }
        .district-count {
            background: #1890ff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .district-branches {
            padding: 0;
        }
        .branch-row {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .branch-row:last-child {
            border-bottom: none;
        }
        .branch-info {
            flex: 1;
        }
        .branch-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        .branch-address {
            color: #666;
            font-size: 13px;
        }
        .gps-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .has-gps {
            background: #52c41a;
        }
        .no-gps {
            background: #ff4d4f;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="page">
            <div class="logo animate__animated animate__fadeInDown"></div>
            <div class="admin-container">
                <div class="stats-card">
                    <div class="stats-title">网点统计</div>
                    <div class="stats-item">
                        <span class="stats-label">总网点数</span>
                        <span class="stats-value">{{ totalBranches }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">有GPS坐标</span>
                        <span class="stats-value">{{ branchesWithGPS }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">无GPS坐标</span>
                        <span class="stats-value">{{ branchesWithoutGPS }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">行政区数量</span>
                        <span class="stats-value">{{ totalDistricts }}</span>
                    </div>
                </div>

                <div class="district-detail">
                    <div v-for="district in districtStats" :key="district.name" class="district-card">
                        <div class="district-header" @click="toggleDistrict(district.name)">
                            <div>
                                <span class="district-name">{{ district.name }}</span>
                                <span class="district-count">{{ district.total }}</span>
                            </div>
                            <van-icon :name="expandedDistricts.includes(district.name) ? 'arrow-up' : 'arrow-down'" />
                        </div>
                        <div v-show="expandedDistricts.includes(district.name)" class="district-branches">
                            <div v-for="branch in district.branches" :key="branch.name" class="branch-row">
                                <div class="branch-info">
                                    <div class="branch-name">{{ branch.name }}</div>
                                    <div class="branch-address">{{ branch.address || '暂无地址信息' }}</div>
                                </div>
                                <div :class="['gps-indicator', branch.gps ? 'has-gps' : 'no-gps']"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="back" @click="back">返回首页</div>
        </div>
    </div>

    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script src="js/bank-data.js"></script>
    <script>
        const { createApp, ref, computed } = Vue
        const app = createApp({
            setup() {
                setMockPage && setMockPage()//添加案例提示语
                
                const bankData = ref({})
                const expandedDistricts = ref([])
                const loading = ref(true)

                // 计算统计数据
                const totalBranches = computed(() => {
                    let total = 0
                    Object.values(bankData.value).forEach(branches => {
                        total += branches.length
                    })
                    return total
                })

                const branchesWithGPS = computed(() => {
                    let count = 0
                    Object.values(bankData.value).forEach(branches => {
                        branches.forEach(branch => {
                            if (branch.gps && branch.gps.trim() !== '') {
                                count++
                            }
                        })
                    })
                    return count
                })

                const branchesWithoutGPS = computed(() => {
                    return totalBranches.value - branchesWithGPS.value
                })

                const totalDistricts = computed(() => {
                    return Object.keys(bankData.value).length
                })

                const districtStats = computed(() => {
                    return Object.entries(bankData.value).map(([name, branches]) => ({
                        name,
                        total: branches.length,
                        withGPS: branches.filter(b => b.gps && b.gps.trim() !== '').length,
                        withoutGPS: branches.filter(b => !b.gps || b.gps.trim() === '').length,
                        branches
                    }))
                })

                // 初始化数据
                const initData = async () => {
                    try {
                        await window.bankDataManager.loadBankData()
                        bankData.value = window.bankDataManager.bankData
                        loading.value = false
                    } catch (error) {
                        console.error('初始化数据失败:', error)
                        loading.value = false
                        vant.showToast('数据加载失败')
                    }
                }

                // 切换区域展开状态
                const toggleDistrict = (districtName) => {
                    const index = expandedDistricts.value.indexOf(districtName)
                    if (index > -1) {
                        expandedDistricts.value.splice(index, 1)
                    } else {
                        expandedDistricts.value.push(districtName)
                    }
                }

                // 返回首页
                const back = () => {
                    location.replace('index.html')
                }

                // 页面加载时初始化
                initData()

                return {
                    bankData,
                    expandedDistricts,
                    loading,
                    totalBranches,
                    branchesWithGPS,
                    branchesWithoutGPS,
                    totalDistricts,
                    districtStats,
                    toggleDistrict,
                    back
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>

</html>
